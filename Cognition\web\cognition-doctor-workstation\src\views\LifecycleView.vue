<template>
  <div class="patient-management">
    <div class="patient-layout">
      <!-- 左侧：正在诊断的患者列表 -->
      <div class="patient-list-section">
        <div class="section-header">
          <h3>正在诊断的患者</h3>
          <el-button type="primary" @click="showAddPatientDialog = true">
            <el-icon><Plus /></el-icon>
            快速添加患者
          </el-button>
        </div>

        <div class="patient-search">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索患者姓名或编号"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>

        <div class="patient-list">
          <div
            v-for="patient in filteredPatients"
            :key="patient.id"
            class="patient-item"
            :class="{ active: selectedPatient?.id === patient.id }"
            @click="selectPatient(patient)"
          >
            <div class="patient-avatar">
              <el-avatar :size="40">
                {{ patient.name.charAt(0) }}
              </el-avatar>
            </div>
            <div class="patient-info">
              <div class="patient-name">{{ patient.name }}</div>
              <div class="patient-details">
                <span class="patient-id">{{ patient.patientId }}</span>
                <span class="patient-age">{{ patient.age }}岁</span>
                <span class="patient-gender">{{ patient.gender }}</span>
              </div>

            </div>
            <div class="patient-actions">
              <el-dropdown @command="handlePatientAction">
                <el-button text>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{ action: 'edit', patient }">编辑</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'tests', patient }">查看测试</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'reports', patient }">生成报告</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>

        <div v-if="filteredPatients.length === 0" class="empty-state">
          <el-empty description="暂无患者数据">
            <el-button type="primary" @click="showAddPatientDialog = true">
              添加患者
            </el-button>
          </el-empty>
        </div>
      </div>

      <!-- 右侧：患者详细信息 -->
      <div class="patient-detail-section">
        <div v-if="selectedPatient" class="patient-detail">
          <!-- 上部分：患者基本信息卡片 -->
          <div class="patient-info-card">


            <el-tabs v-model="activeTab" class="patient-tabs">
            <el-tab-pane label="基本信息" name="info">
              <div class="info-grid">
                <div class="info-item">
                  <label>就诊卡号</label>
                  <span>{{ selectedPatient.patientId }}</span>
                </div>
                <div class="info-item">
                  <label>姓名</label>
                  <span>{{ selectedPatient.name }}</span>
                </div>
                <div class="info-item">
                  <label>联系电话</label>
                  <span>{{ selectedPatient.phone }}</span>
                </div>
                <div class="info-item">
                  <label>身份证号</label>
                  <span>{{ selectedPatient.idCard }}</span>
                </div>
                <div class="info-item">
                  <label>性别</label>
                  <span>{{ selectedPatient.gender }}</span>
                </div>
                <div class="info-item">
                  <label>出生日期</label>
                  <span>{{ selectedPatient.birthDate }}</span>
                </div>
                <div class="info-item">
                  <label>年龄</label>
                  <span>{{ selectedPatient.age }}岁</span>
                </div>
                <div class="info-item">
                  <label>护理等级</label>
                  <span>{{ selectedPatient.careLevel }}</span>
                </div>
                <div class="info-item">
                  <label>既往病史</label>
                  <span>{{ selectedPatient.medicalHistory }}</span>
                </div>
                <div class="info-item">
                  <label>过敏史</label>
                  <span>{{ selectedPatient.allergyHistory }}</span>
                </div>
                <div class="info-item full-width">
                  <label>诊断信息</label>
                  <span>{{ selectedPatient.diagnosisInfo }}</span>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="测试历史" name="tests">
              <div class="test-history">


                <el-table :data="testHistory" class="test-table">
                  <el-table-column prop="taskName" label="任务名称" min-width="120" />
                  <el-table-column label="测试项目" min-width="180">
                    <template #default="{ row }">
                      <span>{{ row.testItems.join(', ') }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="department" label="科室" min-width="80" />
                  <el-table-column prop="doctor" label="主治医师" min-width="90" />
                  <el-table-column prop="testDate" label="测试日期" min-width="100" />
                  <el-table-column prop="status" label="状态" min-width="80">
                    <template #default="{ row }">
                      <el-tag :type="getTestStatusType(row.status)">
                        {{ row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="100">
                    <template #default="{ row }">
                      <el-button text type="primary" @click="viewTestDetail(row)">
                        查看详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>

            <el-tab-pane label="报告中心" name="reports">
              <div class="reports-section">
                <div class="reports-header">

                </div>

                <el-table :data="reports" class="reports-table">
                  <el-table-column prop="reportName" label="报告名称" />
                  <el-table-column prop="generateDate" label="生成日期" />
                  <el-table-column prop="reportType" label="报告类型" />
                  <el-table-column label="操作">
                    <template #default="{ row }">
                      <el-button text type="primary" @click="previewReport(row)">
                        预览
                      </el-button>
                      <el-button text @click="downloadReport(row)">
                        下载
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
          </div>

          <!-- 下部分：测试任务分配 -->
          <div class="test-assignment-section">
            <div class="assignment-header">
              <h3>测试任务</h3>
              <el-button type="primary" @click="showCreateTaskDialog = true">
                <el-icon><Plus /></el-icon>
                创建新任务
              </el-button>
            </div>

            <!-- 当前任务列表 -->
            <div class="current-tasks">
              <div v-if="currentTasks.length === 0" class="no-tasks">
                <el-empty description="暂无测试任务" size="small">
                  <el-button type="primary" @click="showCreateTaskDialog = true">
                    创建测试任务
                  </el-button>
                </el-empty>
              </div>

              <div v-else class="task-list">
                <div
                  v-for="task in currentTasks"
                  :key="task.id"
                  class="task-item"
                  :class="{ 'task-running': task.status === '进行中' }"
                >
                  <div class="task-info">
                    <div class="task-title">{{ task.title }}</div>
                    <div class="task-meta">
                      <span class="task-number">{{ task.taskNumber }}</span>
                      <el-tag :type="getTaskStatusType(task.status)" size="small">
                        {{ task.status }}
                      </el-tag>
                      <span class="task-date">{{ formatDate(task.createDate) }}</span>
                    </div>
                    <div class="task-tests">
                      <el-tag
                        v-for="test in task.tests"
                        :key="test.id"
                        size="small"
                        class="test-tag"
                      >
                        {{ test.name }}
                      </el-tag>
                    </div>
                  </div>

                  <div class="task-progress">
                    <div class="progress-info">
                      <span>{{ task.completedCount }}/{{ task.totalCount }}</span>
                      <el-progress
                        :percentage="Math.round((task.completedCount / task.totalCount) * 100)"
                        :stroke-width="6"
                        :show-text="false"
                      />
                    </div>
                  </div>

                  <div class="task-actions">
                    <div class="action-buttons">


                      <!-- 编辑任务按钮 - 仅在未开始时显示 -->
                      <el-button
                        v-if="task.status === '未开始'"
                        size="small"
                        @click="editTask(task)"
                      >
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>

                      <!-- 查看详情按钮 - 始终显示 -->
                      <el-button
                        size="small"
                        type="primary"
                        @click="viewTaskDetail(task)"
                      >
                        <el-icon><View /></el-icon>
                        详情
                      </el-button>



                      <!-- 删除任务按钮 - 仅在未开始时显示 -->
                      <el-button
                        v-if="task.status === '未开始'"
                        size="small"
                        type="danger"
                        @click="deleteTask(task)"
                      >
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-patient-selected">
          <el-empty description="请从左侧选择一个患者查看详细信息" />
        </div>
      </div>
    </div>

    <!-- 添加患者对话框 -->
    <el-dialog
      v-model="showAddPatientDialog"
      title="添加新患者"
      width="900px"
      @close="resetAddPatientForm"
    >
      <el-form
        ref="addPatientFormRef"
        :model="addPatientForm"
        :rules="addPatientRules"
        label-width="100px"
      >
        <!-- 第一行：姓名、联系电话、身份证号 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="addPatientForm.name" placeholder="请输入患者姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="addPatientForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="addPatientForm.idCard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行：性别、出生日期、护理等级 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="addPatientForm.gender" placeholder="请选择性别" style="width: 100%">
                <el-option label="男" value="男" />
                <el-option label="女" value="女" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出生日期" prop="birthDate">
              <el-date-picker
                v-model="addPatientForm.birthDate"
                type="date"
                placeholder="选择出生日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="护理等级" prop="careLevel">
              <el-select v-model="addPatientForm.careLevel" placeholder="请选择护理等级" style="width: 100%">
                <el-option label="一级护理" value="一级护理" />
                <el-option label="二级护理" value="二级护理" />
                <el-option label="三级护理" value="三级护理" />
                <el-option label="特级护理" value="特级护理" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：既往病史 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="既往病史" prop="medicalHistory">
              <el-input
                v-model="addPatientForm.medicalHistory"
                type="textarea"
                :rows="2"
                placeholder="请输入既往病史（可选）"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第四行：过敏史 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="过敏史" prop="allergyHistory">
              <el-input
                v-model="addPatientForm.allergyHistory"
                type="textarea"
                :rows="2"
                placeholder="请输入过敏史（可选）"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第四行：诊断信息 -->
        <el-form-item label="诊断信息" prop="diagnosisInfo">
          <el-input
            v-model="addPatientForm.diagnosisInfo"
            type="textarea"
            :rows="2"
            placeholder="请输入诊断信息（可选）"
          />
        </el-form-item>


      </el-form>

      <template #footer>
        <el-button @click="showAddPatientDialog = false">取消</el-button>
        <el-button type="primary" @click="addPatient" :loading="addingPatient">
          确定添加
        </el-button>
      </template>
    </el-dialog>

    <!-- 编辑患者对话框 -->
    <el-dialog
      v-model="showEditPatientDialog"
      title="编辑患者信息"
      width="900px"
      @close="resetEditPatientForm"
    >
      <el-form
        ref="editPatientFormRef"
        :model="editPatientForm"
        :rules="addPatientRules"
        label-width="100px"
      >
        <!-- 第一行：姓名、联系电话、身份证号 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="editPatientForm.name" placeholder="请输入患者姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="editPatientForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="editPatientForm.idCard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行：性别、出生日期、护理等级 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="editPatientForm.gender" placeholder="请选择性别" style="width: 100%">
                <el-option label="男" value="男" />
                <el-option label="女" value="女" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出生日期" prop="birthDate">
              <el-date-picker
                v-model="editPatientForm.birthDate"
                type="date"
                placeholder="选择出生日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="护理等级" prop="careLevel">
              <el-select v-model="editPatientForm.careLevel" placeholder="请选择护理等级" style="width: 100%">
                <el-option label="一级护理" value="一级护理" />
                <el-option label="二级护理" value="二级护理" />
                <el-option label="三级护理" value="三级护理" />
                <el-option label="特级护理" value="特级护理" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：既往病史 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="既往病史" prop="medicalHistory">
              <el-input
                v-model="editPatientForm.medicalHistory"
                type="textarea"
                :rows="2"
                placeholder="请输入既往病史（可选）"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第四行：过敏史 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="过敏史" prop="allergyHistory">
              <el-input
                v-model="editPatientForm.allergyHistory"
                type="textarea"
                :rows="2"
                placeholder="请输入过敏史（可选）"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第五行：诊断信息 -->
        <el-form-item label="诊断信息" prop="diagnosisInfo">
          <el-input
            v-model="editPatientForm.diagnosisInfo"
            type="textarea"
            :rows="2"
            placeholder="请输入诊断信息（可选）"
          />
        </el-form-item>

        
      </el-form>

      <template #footer>
        <el-button @click="showEditPatientDialog = false">取消</el-button>
        <el-button type="primary" @click="updatePatient" :loading="editingPatient">
          保存修改
        </el-button>
      </template>
    </el-dialog>

    <!-- 创建测试任务对话框 -->
    <el-dialog
      v-model="showCreateTaskDialog"
      title="创建测试任务"
      width="1800px"
      @close="resetCreateTaskForm"
    >
      <el-form
        ref="createTaskFormRef"
        :model="createTaskForm"
        :rules="createTaskRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="任务标题" prop="title">
              <el-input
                v-model="createTaskForm.title"
                placeholder="自动生成，可修改"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="任务编号" prop="taskNumber">
              <el-input
                v-model="createTaskForm.taskNumber"
                placeholder="自动生成"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="科室" prop="department">
              <el-select v-model="createTaskForm.department" placeholder="请选择科室" @change="generateTaskInfo" style="width: 100%">
                <el-option label="神经内科" value="神经内科" />
                <el-option label="精神科" value="精神科" />
                <el-option label="老年科" value="老年科" />
                <el-option label="心理科" value="心理科" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="测试项目" prop="selectedTests">
          <div class="test-selection">
            <div class="available-tests">
              <div class="tests-header">
                <h4>可选测试项目</h4>
                <el-button type="primary" @click="showTemplateDialog = true">
                  <el-icon><Star /></el-icon>
                  选择模板
                </el-button>
              </div>

              <div class="test-search">
                <el-input
                  v-model="testSearchKeyword"
                  placeholder="搜索测试项目"
                  :prefix-icon="Search"
                  clearable
                />
              </div>

              <div class="test-list">
                <div
                  v-for="test in filteredAvailableTests"
                  :key="test.id"
                  class="test-item"
                  :class="{ selected: isTestSelected(test.id) }"
                  @click="toggleTest(test)"
                >
                  <div class="test-checkbox">
                    <el-checkbox
                      :model-value="isTestSelected(test.id)"
                      @change="toggleTest(test)"
                    />
                  </div>
                  <div class="test-info">
                    <div class="test-name">{{ test.name }}</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="right-panel">
              <!-- 上部分：已选测试项目 (4/6) -->
              <div class="selected-tests-section">
                <div class="selected-tests">
                  <div class="selected-tests-header">
                    <h4>已选测试项目 ({{ createTaskForm.selectedTests.length }})</h4>
                    <el-button
                      v-if="createTaskForm.selectedTests.length > 0"
                      type="danger"
                      size="small"
                      @click="clearAllTests"
                      class="clear-all-btn"
                    >
                      <el-icon><Delete /></el-icon>
                      清除全部
                    </el-button>
                  </div>
                  <div v-if="createTaskForm.selectedTests.length === 0" class="no-selection">
                    请从左侧选择测试项目或使用模板
                  </div>
                  <div v-else class="selected-grid-flow">
                    <div
                      v-for="(element, index) in createTaskForm.selectedTests"
                      :key="element.id"
                      class="grid-item"
                      :data-index="index"
                      :style="getSnakeGridStyle(index)"
                    >
                      <div class="selected-card" :id="`card-${index}`">
                        <div class="card-order">{{ index + 1 }}</div>
                        <div class="card-content">
                          <div class="card-name">{{ element.name }}</div>
                        </div>
                        <el-button
                          text
                          type="danger"
                          size="small"
                          @click="removeTest(element.id)"
                        >
                          <el-icon><Close /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 下部分：测试任务说明 (2/6) -->
              <div class="description-section">
                <div class="task-description-section">
                  <div class="description-header">
                    <h4>测试任务说明</h4>
                    <div class="description-actions">
                      <el-button
                        size="small"
                        @click="saveDescription"
                        :disabled="!createTaskForm.taskDescription.trim()"
                      >
                        <el-icon><Plus /></el-icon>
                        保存记录
                      </el-button>
                      <el-button
                        size="small"
                        @click="showDescriptionHistory = true"
                        :disabled="descriptionHistory.length === 0"
                      >
                        <el-icon><Document /></el-icon>
                        历史记录 ({{ descriptionHistory.length }})
                      </el-button>
                    </div>
                  </div>
                  <el-input
                    v-model="createTaskForm.taskDescription"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入测试任务说明（可选）"
                    maxlength="500"
                    show-word-limit
                    class="description-input"
                  />
                </div>
              </div>
            </div>


          </div>
        </el-form-item>

      </el-form>



      <template #footer>
        <div class="dialog-footer">
          <div class="footer-actions">
            <el-button @click="showCreateTaskDialog = false">取消</el-button>
            <el-button type="primary" @click="createTask" :loading="creatingTask">
              创建任务
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑测试任务对话框 -->
    <el-dialog
      v-model="showEditTaskDialog"
      title="编辑测试任务"
      width="1800px"
      @close="resetEditTaskForm"
    >
      <el-form
        ref="editTaskFormRef"
        :model="editTaskForm"
        :rules="editTaskRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="任务标题" prop="title">
              <el-input
                v-model="editTaskForm.title"
                placeholder="请输入任务标题"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="任务编号" prop="taskNumber">
              <el-input
                v-model="editTaskForm.taskNumber"
                placeholder="任务编号"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="科室" prop="department">
              <el-select v-model="editTaskForm.department" placeholder="请选择科室" style="width: 100%">
                <el-option label="神经内科" value="神经内科" />
                <el-option label="精神科" value="精神科" />
                <el-option label="老年科" value="老年科" />
                <el-option label="心理科" value="心理科" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="测试项目" prop="selectedTests">
          <div class="test-selection">
            <div class="available-tests">
              <div class="tests-header">
                <h4>可选测试项目</h4>
                <el-button type="primary" @click="showTemplateDialog = true">
                  <el-icon><Star /></el-icon>
                  选择模板
                </el-button>
              </div>

              <div class="test-search">
                <el-input
                  v-model="testSearchKeyword"
                  placeholder="搜索测试项目"
                  :prefix-icon="Search"
                  clearable
                />
              </div>

              <div class="test-list">
                <div
                  v-for="test in filteredAvailableTests"
                  :key="test.id"
                  class="test-item"
                  :class="{ selected: isEditTestSelected(test.id) }"
                  @click="toggleEditTest(test)"
                >
                  <div class="test-checkbox">
                    <el-checkbox
                      :model-value="isEditTestSelected(test.id)"
                      @change="toggleEditTest(test)"
                    />
                  </div>
                  <div class="test-info">
                    <div class="test-name">{{ test.name }}</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="right-panel">
              <!-- 上部分：已选测试项目 -->
              <div class="selected-tests-section">
                <div class="selected-tests">
                  <div class="selected-tests-header">
                    <h4>已选测试项目 ({{ editTaskForm.selectedTests.length }})</h4>
                    <el-button
                      v-if="editTaskForm.selectedTests.length > 0"
                      type="danger"
                      size="small"
                      @click="clearAllEditTests"
                      class="clear-all-btn"
                    >
                      <el-icon><Delete /></el-icon>
                      清除全部
                    </el-button>
                  </div>
                  <div v-if="editTaskForm.selectedTests.length === 0" class="no-selection">
                    请从左侧选择测试项目或使用模板
                  </div>
                  <div v-else class="selected-grid-flow">
                    <div
                      v-for="(element, index) in editTaskForm.selectedTests"
                      :key="element.id"
                      class="grid-item"
                      :data-index="index"
                      :style="getSnakeGridStyle(index)"
                    >
                      <div class="selected-card" :id="`edit-card-${index}`">
                        <div class="card-order">{{ index + 1 }}</div>
                        <div class="card-content">
                          <div class="card-name">{{ element.name }}</div>
                        </div>
                        <el-button
                          text
                          type="danger"
                          size="small"
                          @click="removeEditTest(element.id)"
                        >
                          <el-icon><Close /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 下部分：测试任务说明 -->
              <div class="description-section">
                <div class="task-description-section">
                  <div class="description-header">
                    <h4>测试任务说明</h4>
                  </div>
                  <el-input
                    v-model="editTaskForm.taskDescription"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入测试任务说明（可选）"
                    maxlength="500"
                    show-word-limit
                    class="description-input"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <div class="footer-actions">
            <el-button @click="showEditTaskDialog = false">取消</el-button>
            <el-button type="primary" @click="updateTask" :loading="editingTask">
              保存修改
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="showTaskDetailDialog"
      title="任务详情"
      width="1200px"
      @close="viewingTaskData = null"
    >
      <div v-if="viewingTaskData" class="task-detail-content">
        <!-- 基本信息 -->
        <div class="detail-section">
          <div class="section-header">
            <h3 class="section-title">基本信息</h3>
            <!-- 生成报告按钮 - 仅在任务已完成时显示 -->
            <el-button
              v-if="viewingTaskData.status === '已完成'"
              type="primary"
              @click="showReportDialog = true"
            >
              <el-icon><Document /></el-icon>
              生成报告
            </el-button>
          </div>
          <div class="info-grid">
            <div class="info-item">
              <label>任务标题</label>
              <span>{{ viewingTaskData.title }}</span>
            </div>
            <div class="info-item">
              <label>任务编号</label>
              <span>{{ viewingTaskData.taskNumber }}</span>
            </div>
            <div class="info-item">
              <label>科室</label>
              <span>{{ viewingTaskData.department }}</span>
            </div>
            <div class="info-item">
              <label>创建日期</label>
              <span>{{ formatDate(viewingTaskData.createDate) }}</span>
            </div>
            <div class="info-item">
              <label>任务状态</label>
              <el-tag :type="getTaskStatusType(viewingTaskData.status)">
                {{ viewingTaskData.status }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>完成进度</label>
              <span>{{ viewingTaskData.completedCount }}/{{ viewingTaskData.totalCount }}</span>
            </div>
          </div>
        </div>

        <!-- 测试项目 -->
        <div class="detail-section">
          <h3 class="section-title">测试项目</h3>
          <div class="test-items-grid">
            <div
              v-for="(test, index) in viewingTaskData.tests"
              :key="test.id"
              class="test-item-card"
            >
              <div class="test-order">{{ index + 1 }}</div>
              <div class="test-content">
                <div class="test-name">{{ test.name }}</div>
              </div>
              <div class="test-status">
                <el-tag size="small" :type="getTestStatusType(test.status || '待开始')">
                  {{ test.status || '待开始' }}
                </el-tag>
              </div>

            </div>
          </div>
        </div>

        <!-- 进度统计 -->
        <div class="detail-section">
          <h3 class="section-title">进度统计</h3>
          <div class="progress-stats">
            <div class="stat-item">
              <div class="stat-value">{{ viewingTaskData.totalCount }}</div>
              <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ viewingTaskData.completedCount }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ viewingTaskData.totalCount - viewingTaskData.completedCount }}</div>
              <div class="stat-label">待完成</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ Math.round((viewingTaskData.completedCount / viewingTaskData.totalCount) * 100) }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </div>
          <div class="progress-bar">
            <el-progress
              :percentage="Math.round((viewingTaskData.completedCount / viewingTaskData.totalCount) * 100)"
              :stroke-width="12"
              :show-text="true"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showTaskDetailDialog = false">关闭</el-button>
          <el-button
            v-if="viewingTaskData?.status === '未开始'"
            type="primary"
            @click="editTask(viewingTaskData!)"
          >
            编辑任务
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 报告生成对话框 -->
    <el-dialog
      v-model="showReportDialog"
      title="生成认知测试报告"
      width="90%"
      :close-on-click-modal="false"
      :modal="true"
      :show-close="true"
      class="report-dialog"
    >
      <div class="report-generator">
        <!-- 左侧A4预览区域 -->
        <div class="report-preview-container">
          <div class="a4-pages" id="reportPreview">
            <!-- 第一页 -->
            <div class="a4-page page-1">
              <div class="report-header">
                <h1>认知功能测试报告</h1>
                <div class="report-info">
                  <div class="info-row">
                    <span><strong>患者姓名：</strong>{{ selectedPatient?.name || '张三' }}</span>
                    <span><strong>性别：</strong>{{ selectedPatient?.gender || '男' }}</span>
                  </div>
                  <div class="info-row">
                    <span><strong>年龄：</strong>{{ selectedPatient?.age || '65' }}岁</span>
                    <span><strong>身份证号：</strong>{{ selectedPatient?.idCard || '110101195901011234' }}</span>
                  </div>
                  <div class="info-row">
                    <span><strong>任务编号：</strong>{{ viewingTaskData?.taskNumber }}</span>
                    <span><strong>科室：</strong>{{ viewingTaskData?.department }}</span>
                  </div>
                  <div class="info-row">
                    <span><strong>测试日期：</strong>{{ viewingTaskData?.createDate }}</span>
                    <span><strong>报告日期：</strong>{{ new Date().toLocaleDateString('zh-CN') }}</span>
                  </div>
                </div>
              </div>

              <div class="report-content">
                <h2>一、测试项目及结果</h2>
                <table class="test-results-table">
                  <thead>
                    <tr>
                      <th width="10%">序号</th>
                      <th width="30%">测试项目</th>
                      <th width="15%">状态</th>
                      <th width="20%">得分</th>
                      <th width="25%">备注</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(test, index) in viewingTaskData?.tests" :key="test.id">
                      <td>{{ index + 1 }}</td>
                      <td>{{ test.name }}</td>
                      <td>{{ test.status }}</td>
                      <td>{{ Math.floor(Math.random() * 30) + 70 }}/100</td>
                      <td>正常范围</td>
                    </tr>
                  </tbody>
                </table>

                <h2>二、测试结果分析</h2>
                <div class="analysis-content">
                  <p>本次认知功能测试包含多个维度的评估，涵盖记忆力、注意力、执行功能、语言能力等认知域。</p>
                  <p>测试结果显示：</p>
                  <ul>
                    <li>记忆功能：通过PDQ5量表和MMSE量表评估，结果显示记忆功能基本正常。</li>
                    <li>注意力：通过时钟绘制测试评估，显示注意力集中度良好。</li>
                    <li>执行功能：各项测试均在正常范围内。</li>
                  </ul>
                </div>

                <div class="page-footer">
                  <span>第 1 页，共 2 页</span>
                </div>
              </div>
            </div>

            <!-- 第二页 -->
            <div class="a4-page page-2">
              <div class="report-header-simple">
                <h3>认知功能测试报告（续）</h3>
                <div class="patient-info-simple">
                  <span>患者：{{ selectedPatient?.name || '张三' }}</span>
                  <span>任务编号：{{ viewingTaskData?.taskNumber }}</span>
                </div>
              </div>

              <div class="report-content">
                <h2>三、诊断结论</h2>
                <div class="diagnosis-content">
                  <p v-if="diagnosisConclusion">{{ diagnosisConclusion }}</p>
                  <p v-else class="placeholder">请在右侧输入诊断结论</p>
                </div>

                <h2>四、建议</h2>
                <div class="recommendations">
                  <p>基于本次认知功能测试结果，建议如下：</p>
                  <ol>
                    <li>定期进行认知功能复查，建议每6个月复查一次。</li>
                    <li>保持健康的生活方式，包括规律作息、适量运动、均衡饮食。</li>
                    <li>进行适当的认知训练，如阅读、下棋、学习新技能等。</li>
                    <li>如有认知功能下降的症状，请及时就医。</li>
                  </ol>
                </div>

                <div class="report-signature">
                  <div class="signature-section">
                    <div class="signature-item">
                      <span>报告医生：</span>
                      <span class="signature-line">_________________</span>
                    </div>
                    <div class="signature-item">
                      <span>审核医生：</span>
                      <span class="signature-line">_________________</span>
                    </div>
                    <div class="signature-item">
                      <span>报告日期：</span>
                      <span>{{ new Date().toLocaleDateString('zh-CN') }}</span>
                    </div>
                  </div>
                </div>

                <div class="page-footer">
                  <span>第 2 页，共 2 页</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧控制面板 -->
        <div class="report-controls">
          <!-- 报告模板选择 -->
          <div class="control-section">
            <h4>选择报告模板</h4>
            <el-radio-group v-model="selectedReportTemplate" size="small">
              <el-radio
                v-for="template in reportTemplates"
                :key="template.id"
                :value="template.id"
                class="template-radio"
              >
                {{ template.name }}
              </el-radio>
            </el-radio-group>
          </div>

          <!-- 诊断结论编辑 -->
          <div class="control-section">
            <div class="diagnosis-header">
              <h4>诊断结论</h4>
              <el-button
                size="small"
                type="primary"
                @click="showDiagnosisTemplateDialog = true"
                class="template-trigger-btn"
              >
                <el-icon><Document /></el-icon>
                模板
              </el-button>
            </div>
            <el-input
              v-model="diagnosisConclusion"
              type="textarea"
              :rows="8"
              placeholder="请输入诊断结论，或点击模板按钮选择模板..."
              class="diagnosis-input"
            />
            <div class="save-section">
              <el-button
                type="success"
                @click="saveDiagnosisConclusion"
                :disabled="!diagnosisConclusion.trim()"
                class="save-btn"
              >
                <el-icon><Check /></el-icon>
                保存诊断结论
              </el-button>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="control-section">
            <h4>操作</h4>
            <div class="action-buttons">
              <el-button @click="printReport" icon="Printer">打印报告</el-button>
              <el-button type="primary" @click="downloadPDF" icon="Download">下载PDF</el-button>
            </div>
          </div>
        </div>


      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showReportDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 测试模板选择对话框 -->
    <el-dialog
      v-model="showTemplateDialog"
      title="选择认知模板"
      width="800px"
      @close="resetTemplateSelection"
    >
      <div class="template-selection-container">
        <div class="template-search-section">
          <el-input
            v-model="templateSearchKeyword"
            placeholder="搜索模板名称或描述"
            :prefix-icon="Search"
            clearable
          />
        </div>

        <div class="template-list-container">
          <div
            v-for="template in filteredTemplates"
            :key="template.id"
            class="template-item-container"
            :class="{ 'template-selected': selectedTemplate?.id === template.id }"
            @click="selectTemplate(template)"
          >
            <div class="template-radio-section">
              <el-radio
                :model-value="selectedTemplate?.id"
                :value="template.id"
                @change="selectTemplate(template)"
              />
            </div>
            <div class="template-content-section">
              <div class="template-title">{{ template.name }}</div>
              <div class="template-desc">{{ template.description }}</div>
              <div class="template-test-list">
                <span
                  v-for="(test, index) in template.tests"
                  :key="test.id"
                  class="test-item-name"
                >
                  {{ test.name }}<span v-if="index < template.tests.length - 1">、</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showTemplateDialog = false">取消</el-button>
        <el-button type="primary" @click="applyTemplate" :disabled="!selectedTemplate">
          应用模板
        </el-button>
      </template>
    </el-dialog>

    <!-- 诊断结论模板选择对话框 -->
    <el-dialog
      v-model="showDiagnosisTemplateDialog"
      title="选择诊断结论模板"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="diagnosis-template-selection">
        <div class="template-list">
          <div
            v-for="template in diagnosisTemplates"
            :key="template.id"
            class="diagnosis-template-item"
            :class="{ active: selectedDiagnosisTemplateId === template.id }"
            @click="selectedDiagnosisTemplateId = template.id"
          >
            <div class="template-header">
              <el-radio
                v-model="selectedDiagnosisTemplateId"
                :value="template.id"
                class="template-radio"
              >
                {{ template.name }}
              </el-radio>
              <el-tag :type="template.type" size="small">{{ template.level }}</el-tag>
            </div>
            <div class="template-preview">
              {{ template.content }}
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDiagnosisTemplateDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="applyDiagnosisTemplate"
            :disabled="!selectedDiagnosisTemplateId"
          >
            应用模板
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 测试任务说明历史记录对话框 -->
    <el-dialog
      v-model="showDescriptionHistory"
      title="测试任务说明历史记录"
      width="800px"
      @close="showDescriptionHistory = false"
    >
      <div class="description-history">
        <div v-if="descriptionHistory.length === 0" class="no-history">
          暂无历史记录
        </div>
        <div v-else class="history-list">
          <div
            v-for="(item, index) in descriptionHistory"
            :key="item.id"
            class="history-item"
          >
            <div class="history-content">
              <div class="history-text">{{ item.content }}</div>
              <div class="history-meta">
                <span class="history-date">{{ formatDateTime(item.createTime) }}</span>
                <span class="history-usage">使用次数: {{ item.usageCount }}</span>
              </div>
            </div>
            <div class="history-actions">
              <el-button
                size="small"
                type="primary"
                @click="useDescription(item)"
              >
                引用
              </el-button>
              <el-button
                size="small"
                @click="editDescription(item, index)"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deleteDescription(index)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showDescriptionHistory = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 编辑历史记录对话框 -->
    <el-dialog
      v-model="showEditDescription"
      title="编辑历史记录"
      width="600px"
      @close="resetEditDescription"
    >
      <el-input
        v-model="editingDescription"
        type="textarea"
        :rows="4"
        placeholder="请输入测试任务说明"
        maxlength="500"
        show-word-limit
      />

      <template #footer>
        <el-button @click="showEditDescription = false">取消</el-button>
        <el-button type="primary" @click="saveEditDescription">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  MoreFilled,
  Edit,
  Document,
  Close,
  Star,
  Delete,
  View,
  Printer,
  Download,
  Check
} from '@element-plus/icons-vue'

// 接口定义
interface Patient {
  id: string
  patientId: string
  name: string
  phone: string
  idCard: string
  gender: string
  birthDate: string
  age: number
  diagnosisStatus: string
  medicalHistory: string
  allergyHistory: string
  careLevel: string
  diagnosisInfo: string
  emergencyContact: string
  emergencyPhone: string
  status: string
  lastVisit: string
  notes?: string
  testCount?: number
  completedTests?: number
  pendingTests?: number
}

interface TestRecord {
  id: string
  taskName: string
  testName: string
  testItems: string[]
  department: string
  doctor: string
  testDate: string
  status: string
  score?: string
}

interface Report {
  id: string
  reportName: string
  generateDate: string
  reportType: string
}

interface TestItem {
  id: string
  name: string
  duration: number
  description: string
  category: string
  status?: string // 测试项目状态：待开始、进行中、已完成
}

interface TestTask {
  id: string
  title: string
  taskNumber: string
  department: string
  patientId: string
  status: string
  createDate: string
  tests: TestItem[]
  completedCount: number
  totalCount: number
}

interface TestTemplate {
  id: string
  name: string
  category: string
  description: string
  tests: TestItem[]
  totalDuration: number
}

// 响应式数据
const searchKeyword = ref('')
const selectedPatient = ref<Patient | null>(null)
const activeTab = ref('info')
const showAddPatientDialog = ref(false)
const showEditPatientDialog = ref(false)
const addingPatient = ref(false)
const editingPatient = ref(false)
const editingPatientData = ref<Patient | null>(null)

// 测试任务相关
const showCreateTaskDialog = ref(false)
const showEditTaskDialog = ref(false)
const showTaskDetailDialog = ref(false)
const creatingTask = ref(false)
const editingTask = ref(false)
const currentTasks = ref<TestTask[]>([])
const editingTaskData = ref<TestTask | null>(null)
const viewingTaskData = ref<TestTask | null>(null)

// 测试模板相关
const showTemplateDialog = ref(false)
const templateSearchKeyword = ref('')
const selectedTemplate = ref<TestTemplate | null>(null)

// 测试项目搜索
const testSearchKeyword = ref('')

// 可用测试项目
const availableTests = ref<TestItem[]>([
  {
    id: '1',
    name: 'PDQ-5测试',
    duration: 15,
    description: '评估记忆力、注意力、思维反应和认知灵活性',
    category: '认知评估'
  },
  {
    id: '2',
    name: 'Hopkins测试',
    duration: 20,
    description: '测试即时记忆和延迟记忆能力',
    category: '记忆测试'
  },
  {
    id: '3',
    name: '顺背/倒背测试',
    duration: 25,
    description: '评估工作记忆和注意力持续性',
    category: '记忆测试'
  },
  {
    id: '4',
    name: 'Stroop色词测试',
    duration: 10,
    description: '测试抑制控制能力和认知灵活性',
    category: '注意力测试'
  },
  {
    id: '5',
    name: '连线测试',
    duration: 12,
    description: '评估执行功能和视觉-运动协调',
    category: '执行功能'
  },
  {
    id: '6',
    name: '词语流畅性测试',
    duration: 8,
    description: '评估语言功能和语义记忆',
    category: '语言测试'
  },
  {
    id: '7',
    name: '持续性操作测试',
    duration: 18,
    description: '测试注意力持续性和反应抑制',
    category: '注意力测试'
  },
  {
    id: '8',
    name: 'DSST测试',
    duration: 15,
    description: '评估处理速度和视觉-运动协调',
    category: '处理速度'
  },
  {
    id: '9',
    name: '情感启动效应测试',
    duration: 12,
    description: '评估情绪认知和情感处理能力',
    category: '情绪认知'
  }
])

// 测试模板数据 - 直接定义测试项目数据
const testTemplates = ref<TestTemplate[]>([
  {
    id: '1',
    name: '基础认知评估套餐',
    category: '标准评估',
    description: '适用于初次认知筛查，包含基本的认知功能评估项目',
    tests: [
      { id: '1', name: 'PDQ-5测试', duration: 15, description: '评估记忆力、注意力、思维反应和认知灵活性', category: '认知评估' },
      { id: '3', name: '顺背/倒背测试', duration: 25, description: '评估工作记忆和注意力持续性', category: '记忆测试' },
      { id: '5', name: '连线测试', duration: 12, description: '评估执行功能和视觉-运动协调', category: '执行功能' }
    ],
    totalDuration: 52
  },
  {
    id: '2',
    name: '全面认知评估套餐',
    category: '深度评估',
    description: '全面的认知功能评估，适用于详细诊断和评估',
    tests: [
      { id: '1', name: 'PDQ-5测试', duration: 15, description: '评估记忆力、注意力、思维反应和认知灵活性', category: '认知评估' },
      { id: '2', name: 'Hopkins测试', duration: 20, description: '测试即时记忆和延迟记忆能力', category: '记忆测试' },
      { id: '4', name: 'Stroop色词测试', duration: 10, description: '测试抑制控制能力和认知灵活性', category: '注意力测试' },
      { id: '5', name: '连线测试', duration: 12, description: '评估执行功能和视觉-运动协调', category: '执行功能' },
      { id: '6', name: '词语流畅性测试', duration: 8, description: '评估语言功能和语义记忆', category: '语言测试' },
      { id: '8', name: 'DSST测试', duration: 15, description: '评估处理速度和视觉-运动协调', category: '处理速度' }
    ],
    totalDuration: 90
  },
  {
    id: '3',
    name: '注意力专项评估',
    category: '专项评估',
    description: '针对注意力功能的专项评估，包含多种注意力测试',
    tests: [
      { id: '4', name: 'Stroop色词测试', duration: 10, description: '测试抑制控制能力和认知灵活性', category: '注意力测试' },
      { id: '7', name: '持续性操作测试', duration: 18, description: '测试注意力持续性和反应抑制', category: '注意力测试' },
      { id: '8', name: 'DSST测试', duration: 15, description: '评估处理速度和视觉-运动协调', category: '处理速度' }
    ],
    totalDuration: 43
  },
  {
    id: '4',
    name: '记忆功能评估',
    category: '专项评估',
    description: '专门评估记忆功能的测试组合',
    tests: [
      { id: '2', name: 'Hopkins测试', duration: 20, description: '测试即时记忆和延迟记忆能力', category: '记忆测试' },
      { id: '3', name: '顺背/倒背测试', duration: 25, description: '评估工作记忆和注意力持续性', category: '记忆测试' }
    ],
    totalDuration: 45
  },
  {
    id: '5',
    name: '语言功能评估',
    category: '专项评估',
    description: '重点评估语言和言语功能的测试组合',
    tests: [
      { id: '6', name: '词语流畅性测试', duration: 8, description: '评估语言功能和语义记忆', category: '语言测试' },
      { id: '1', name: 'PDQ-5测试', duration: 15, description: '评估记忆力、注意力、思维反应和认知灵活性', category: '认知评估' }
    ],
    totalDuration: 23
  },
  {
    id: '6',
    name: '情绪认知评估',
    category: '专项评估',
    description: '专注于情绪认知和情感处理能力的评估',
    tests: [
      { id: '9', name: '情感启动效应测试', duration: 12, description: '评估情绪认知和情感处理能力', category: '情绪认知' },
      { id: '1', name: 'PDQ-5测试', duration: 15, description: '评估记忆力、注意力、思维反应和认知灵活性', category: '认知评估' }
    ],
    totalDuration: 27
  }
])

// 创建任务表单
const createTaskForm = ref({
  title: '',
  taskNumber: '',
  department: '',
  selectedTests: [] as TestItem[],
  taskDescription: ''
})

// 编辑任务表单
const editTaskForm = ref({
  title: '',
  taskNumber: '',
  department: '',
  selectedTests: [] as TestItem[],
  taskDescription: ''
})

// 表单验证规则
const createTaskRules = {
  department: [
    { required: true, message: '请选择科室', trigger: 'change' }
  ],
  selectedTests: [
    { required: true, message: '请至少选择一个测试项目', trigger: 'change' }
  ]
}

const editTaskRules = {
  department: [
    { required: true, message: '请选择科室', trigger: 'change' }
  ],
  selectedTests: [
    { required: true, message: '请至少选择一个测试项目', trigger: 'change' }
  ]
}

const createTaskFormRef = ref()
const editTaskFormRef = ref()

// 患者列表数据
const patients = ref<Patient[]>([
  {
    id: '1',
    patientId: 'P001',
    name: '张三',
    phone: '13800138001',
    idCard: '110101195803151234',
    gender: '男',
    birthDate: '1958-03-15',
    age: 65,
    diagnosisStatus: '正在诊断',
    medicalHistory: '高血压病史10年，糖尿病病史5年',
    allergyHistory: '青霉素过敏',
    careLevel: '一级护理',
    diagnosisInfo: '轻度认知障碍，疑似阿尔茨海默病早期',
    emergencyContact: '李四',
    emergencyPhone: '13800138002',
    status: '正在诊断',
    lastVisit: '2024-01-15',
    notes: '轻度认知障碍，需要定期复查',
    testCount: 5,
    completedTests: 3,
    pendingTests: 2
  },
  {
    id: '2',
    patientId: 'P002',
    name: '王芳',
    phone: '13800138003',
    idCard: '110101195108225678',
    gender: '女',
    birthDate: '1951-08-22',
    age: 72,
    diagnosisStatus: '待复查',
    medicalHistory: '冠心病病史8年，骨质疏松',
    allergyHistory: '无已知过敏史',
    careLevel: '二级护理',
    diagnosisInfo: '中度认知功能下降，血管性痴呆可能',
    emergencyContact: '王明',
    emergencyPhone: '13800138004',
    status: '待复查',
    lastVisit: '2024-01-10',
    notes: '记忆力下降明显',
    testCount: 3,
    completedTests: 3,
    pendingTests: 0
  },
  {
    id: '3',
    patientId: 'P003',
    name: '李明',
    phone: '13800138005',
    idCard: '110101196512059012',
    gender: '男',
    birthDate: '1965-12-05',
    age: 58,
    diagnosisStatus: '正在诊断',
    medicalHistory: '无重大疾病史',
    allergyHistory: '海鲜过敏',
    careLevel: '三级护理',
    diagnosisInfo: '主观认知下降，需进一步评估',
    emergencyContact: '李红',
    emergencyPhone: '13800138006',
    status: '正在诊断',
    lastVisit: '2024-01-20',
    notes: '',
    testCount: 2,
    completedTests: 1,
    pendingTests: 1
  }
])

// 测试历史数据
const testHistory = ref<TestRecord[]>([
  {
    id: '1',
    taskName: '认知评估任务A',
    testName: '词语流畅性测试',
    testItems: ['语义流畅性', '语音流畅性', '转换流畅性'],
    department: '神经内科',
    doctor: '张医生',
    testDate: '2024-01-15',
    status: '待评分',
    score: '-'
  },
  {
    id: '2',
    taskName: '记忆功能评估',
    testName: '顺背/倒背测试',
    testItems: ['顺背数字', '倒背数字'],
    department: '神经内科',
    doctor: '李医生',
    testDate: '2024-01-15',
    status: '已完成',
    score: '85分'
  },
  {
    id: '3',
    taskName: '注意力评估',
    testName: '持续性操作测试',
    testItems: ['持续注意', '选择注意', '分配注意'],
    department: '心理科',
    doctor: '王医生',
    testDate: '2024-01-10',
    status: '已完成',
    score: '78分'
  },
  {
    id: '4',
    taskName: '基础认知评估',
    testName: 'PDQ-5测试',
    testItems: ['记忆力评估', '注意力评估', '认知灵活性评估'],
    department: '神经内科',
    doctor: '张医生',
    testDate: '2024-01-16',
    status: '已完成',
    score: '92分'
  },
  {
    id: '5',
    taskName: '情绪认知评估',
    testName: '情感启动效应测试',
    testItems: ['情绪识别', '情感处理', '启动效应'],
    department: '心理科',
    doctor: '王医生',
    testDate: '2024-01-14',
    status: '已完成',
    score: '88分'
  }
])

// 报告数据
const reports = ref<Report[]>([
  {
    id: '1',
    reportName: '认知能力评估报告',
    generateDate: '2024-01-16',
    reportType: '综合报告'
  },
  {
    id: '2',
    reportName: '词语流畅性专项报告',
    generateDate: '2024-01-12',
    reportType: '专项报告'
  }
])

// 添加患者表单
const addPatientForm = ref({
  name: '',
  phone: '',
  idCard: '',
  gender: '',
  birthDate: '',
  medicalHistory: '',
  allergyHistory: '',
  careLevel: '',
  diagnosisInfo: '',
  notes: ''
})

// 表单验证规则
const addPatientRules = {
  name: [
    { required: true, message: '请输入患者姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  idCard: [
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号码', trigger: 'blur' }
  ]
}

const addPatientFormRef = ref()

// 编辑患者表单
const editPatientForm = ref({
  name: '',
  phone: '',
  idCard: '',
  gender: '',
  birthDate: '',
  medicalHistory: '',
  allergyHistory: '',
  careLevel: '',
  diagnosisInfo: '',
  notes: ''
})

const editPatientFormRef = ref()

// 计算属性
const filteredPatients = computed(() => {
  if (!searchKeyword.value) {
    return patients.value
  }
  return patients.value.filter(patient =>
    patient.name.includes(searchKeyword.value) ||
    patient.patientId.includes(searchKeyword.value)
  )
})

const estimatedDuration = computed(() => {
  return createTaskForm.value.selectedTests.reduce((total, test) => total + test.duration, 0)
})

const filteredTemplates = computed(() => {
  if (!templateSearchKeyword.value) {
    return testTemplates.value
  }
  return testTemplates.value.filter(template =>
    template.name.includes(templateSearchKeyword.value) ||
    template.description.includes(templateSearchKeyword.value) ||
    template.category.includes(templateSearchKeyword.value)
  )
})

const filteredAvailableTests = computed(() => {
  if (!testSearchKeyword.value) {
    return availableTests.value
  }
  return availableTests.value.filter(test =>
    test.name.includes(testSearchKeyword.value) ||
    test.category.includes(testSearchKeyword.value) ||
    test.description.includes(testSearchKeyword.value)
  )
})

// 方法
const selectPatient = (patient: Patient) => {
  selectedPatient.value = patient
  activeTab.value = 'info'
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '正在诊断': 'warning',
    '待复查': 'info',
    '已完成': 'success'
  }
  return statusMap[status] || 'info'
}

const getTestStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待开始': 'info',
    '进行中': 'warning',
    '已完成': 'success',
    '待评分': 'warning',
    '已取消': 'danger'
  }
  return statusMap[status] || 'info'
}

const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

const calculateAge = (birthDate: string) => {
  const birth = new Date(birthDate)
  const today = new Date()
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }

  return age
}

const handlePatientAction = ({ action, patient }: { action: string, patient: Patient }) => {
  switch (action) {
    case 'edit':
      editPatient(patient)
      break
    case 'tests':
      selectPatient(patient)
      activeTab.value = 'tests'
      break
    case 'reports':
      selectPatient(patient)
      activeTab.value = 'reports'
      break
  }
}

const editPatient = (patient: Patient) => {
  editingPatientData.value = patient
  // 填充编辑表单
  editPatientForm.value = {
    name: patient.name,
    phone: patient.phone,
    idCard: patient.idCard,
    gender: patient.gender,
    birthDate: patient.birthDate,
    medicalHistory: patient.medicalHistory,
    allergyHistory: patient.allergyHistory,
    careLevel: patient.careLevel,
    diagnosisInfo: patient.diagnosisInfo,
    notes: patient.notes || ''
  }
  showEditPatientDialog.value = true
}

const deletePatient = async (patient: Patient) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除患者 ${patient.name} 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = patients.value.findIndex(p => p.id === patient.id)
    if (index > -1) {
      patients.value.splice(index, 1)
      if (selectedPatient.value?.id === patient.id) {
        selectedPatient.value = null
      }
      ElMessage.success('患者删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const addPatient = async () => {
  if (!addPatientFormRef.value) return

  try {
    await addPatientFormRef.value.validate()
    addingPatient.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const newPatient: Patient = {
      id: Date.now().toString(),
      patientId: `P${String(patients.value.length + 1).padStart(3, '0')}`,
      name: addPatientForm.value.name,
      phone: addPatientForm.value.phone,
      idCard: addPatientForm.value.idCard || '',
      gender: addPatientForm.value.gender || '未填写',
      birthDate: addPatientForm.value.birthDate || '',
      age: addPatientForm.value.birthDate ? calculateAge(addPatientForm.value.birthDate) : 0,
      diagnosisStatus: '正在诊断',
      medicalHistory: addPatientForm.value.medicalHistory || '无',
      allergyHistory: addPatientForm.value.allergyHistory || '无已知过敏史',
      careLevel: addPatientForm.value.careLevel || '三级护理',
      diagnosisInfo: addPatientForm.value.diagnosisInfo || '待评估',
      emergencyContact: '',
      emergencyPhone: '',
      status: '正在诊断',
      lastVisit: new Date().toISOString().split('T')[0],
      notes: addPatientForm.value.notes,
      testCount: 0,
      completedTests: 0,
      pendingTests: 0
    }

    patients.value.unshift(newPatient)
    showAddPatientDialog.value = false
    selectedPatient.value = newPatient

    ElMessage.success('患者添加成功')
  } catch (error) {
    console.error('添加患者失败:', error)
  } finally {
    addingPatient.value = false
  }
}

const resetAddPatientForm = () => {
  addPatientForm.value = {
    name: '',
    phone: '',
    idCard: '',
    gender: '',
    birthDate: '',
    medicalHistory: '',
    allergyHistory: '',
    careLevel: '',
    diagnosisInfo: '',
    notes: ''
  }
  addPatientFormRef.value?.resetFields()
}

const updatePatient = async () => {
  if (!editPatientFormRef.value || !editingPatientData.value) return

  try {
    await editPatientFormRef.value.validate()
    editingPatient.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新患者数据
    const patientIndex = patients.value.findIndex(p => p.id === editingPatientData.value!.id)
    if (patientIndex > -1) {
      const updatedPatient = {
        ...patients.value[patientIndex],
        name: editPatientForm.value.name,
        phone: editPatientForm.value.phone,
        idCard: editPatientForm.value.idCard,
        gender: editPatientForm.value.gender,
        birthDate: editPatientForm.value.birthDate,
        age: editPatientForm.value.birthDate ? calculateAge(editPatientForm.value.birthDate) : patients.value[patientIndex].age,
        medicalHistory: editPatientForm.value.medicalHistory,
        allergyHistory: editPatientForm.value.allergyHistory,
        careLevel: editPatientForm.value.careLevel,
        diagnosisInfo: editPatientForm.value.diagnosisInfo,
        notes: editPatientForm.value.notes
      }

      patients.value[patientIndex] = updatedPatient

      // 如果当前选中的是被编辑的患者，更新选中状态
      if (selectedPatient.value?.id === editingPatientData.value.id) {
        selectedPatient.value = updatedPatient
      }
    }

    showEditPatientDialog.value = false
    ElMessage.success('患者信息更新成功')
  } catch (error) {
    console.error('更新患者失败:', error)
  } finally {
    editingPatient.value = false
  }
}

const resetEditPatientForm = () => {
  editPatientForm.value = {
    name: '',
    phone: '',
    idCard: '',
    gender: '',
    birthDate: '',
    medicalHistory: '',
    allergyHistory: '',
    careLevel: '',
    diagnosisInfo: '',
    notes: ''
  }
  editingPatientData.value = null
  editPatientFormRef.value?.resetFields()
}

const viewTestDetail = (test: TestRecord) => {
  ElMessage.info('查看测试详情功能开发中...')
}

const scoreTest = (test: TestRecord) => {
  ElMessage.info('测试评分功能开发中...')
}

const generateReport = () => {
  ElMessage.info('生成报告功能开发中...')
}

const previewReport = (report: Report) => {
  ElMessage.info('预览报告功能开发中...')
}

const downloadReport = (report: Report) => {
  ElMessage.info('下载报告功能开发中...')
}

// 测试任务相关方法
const getTaskStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '未开始': 'info',
    '进行中': 'warning',
    '已完成': 'success'
  }
  return statusMap[status] || 'info'
}



// 计算任务状态的函数
const calculateTaskStatus = (task: any) => {
  const completedTests = task.tests.filter((test: any) => test.status === '已完成').length
  const startedTests = task.tests.filter((test: any) => test.status === '进行中' || test.status === '已完成').length

  if (startedTests === 0) {
    return '未开始'
  } else if (completedTests < task.tests.length) {
    return '进行中'
  } else {
    return '已完成'
  }
}

// 更新任务状态的函数
const updateTaskStatus = (task: any) => {
  // 更新完成数量
  task.completedCount = task.tests.filter((test: any) => test.status === '已完成').length
  // 更新任务状态
  task.status = calculateTaskStatus(task)
}

// 报告生成相关函数
const insertTemplate = (type: string) => {
  const templates = {
    normal: '根据本次认知功能测试结果，患者各项认知指标均在正常范围内，认知功能良好。建议定期复查，保持健康的生活方式。',
    mild: '根据本次认知功能测试结果，患者存在轻度认知功能异常，主要表现在记忆力和注意力方面。建议进一步观察，适当进行认知训练。',
    moderate: '根据本次认知功能测试结果，患者存在中度认知功能障碍，多个认知域受到影响。建议进行详细的神经心理学评估，制定个性化的干预方案。',
    severe: '根据本次认知功能测试结果，患者存在重度认知功能障碍，严重影响日常生活能力。建议立即进行全面的医学评估，考虑药物治疗和综合干预。'
  }
  diagnosisConclusion.value = templates[type as keyof typeof templates] || ''
}

const applyDiagnosisTemplate = () => {
  const selectedTemplate = diagnosisTemplates.value.find(t => t.id === selectedDiagnosisTemplateId.value)
  if (selectedTemplate) {
    diagnosisConclusion.value = selectedTemplate.content
    showDiagnosisTemplateDialog.value = false
    selectedDiagnosisTemplateId.value = ''
    ElMessage.success('模板应用成功')
  }
}

const printReport = () => {
  const printWindow = window.open('', '_blank')
  if (printWindow) {
    const reportContent = document.getElementById('reportPreview')?.innerHTML || ''
    printWindow.document.write(`
      <html>
        <head>
          <title>认知测试报告</title>
          <style>
            body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
            .report-header { text-align: center; margin-bottom: 30px; }
            .report-header h2 { margin: 0; font-size: 24px; }
            .report-info { margin-top: 20px; text-align: left; }
            .report-info p { margin: 5px 0; }
            .test-results-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            .test-results-table th, .test-results-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .test-results-table th { background-color: #f5f5f5; }
            .diagnosis-content { margin: 20px 0; padding: 15px; border: 1px solid #ddd; min-height: 100px; }
            .report-footer { margin-top: 50px; }
            .report-footer p { margin: 10px 0; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>${reportContent}</body>
      </html>
    `)
    printWindow.document.close()
    printWindow.print()
  }
}

const downloadPDF = async () => {
  try {
    // 暂时使用浏览器打印功能代替PDF下载
    ElMessage.info('PDF下载功能开发中，请使用打印功能')
    printReport()
  } catch (error) {
    console.error('PDF生成失败:', error)
    ElMessage.error('PDF生成失败，请检查网络连接或稍后重试')
  }
}

const saveDiagnosisConclusion = () => {
  if (!diagnosisConclusion.value.trim()) {
    ElMessage.warning('请输入诊断结论')
    return
  }

  try {
    // 这里可以添加保存到后端的逻辑
    // await saveDiagnosisToServer(viewingTaskData.value?.taskNumber, diagnosisConclusion.value)

    ElMessage.success('诊断结论保存成功')
  } catch (error) {
    console.error('保存诊断结论失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  }
}





const generateTaskInfo = () => {
  if (!createTaskForm.value.department || !selectedPatient.value) return

  const now = new Date()
  const dateStr = now.toISOString().split('T')[0].replace(/-/g, '')
  const departmentCode = getDepartmentCode(createTaskForm.value.department)

  // 生成任务标题
  createTaskForm.value.title = `${dateStr} ${createTaskForm.value.department} 认知测试`

  // 生成任务编号
  const serialNumber = String(Math.floor(Math.random() * 99999) + 1).padStart(5, '0')
  createTaskForm.value.taskNumber = `${departmentCode}${dateStr}${serialNumber}`
}

const getDepartmentCode = (department: string) => {
  const codeMap: Record<string, string> = {
    '神经内科': 'SJ',
    '精神科': 'JS',
    '老年科': 'LN',
    '心理科': 'XL'
  }
  return codeMap[department] || 'XX'
}

const isTestSelected = (testId: string) => {
  return createTaskForm.value.selectedTests.some(test => test.id === testId)
}

const toggleTest = (test: TestItem) => {
  const index = createTaskForm.value.selectedTests.findIndex(t => t.id === test.id)
  if (index > -1) {
    createTaskForm.value.selectedTests.splice(index, 1)
  } else {
    createTaskForm.value.selectedTests.push(test)
  }
}

const removeTest = (testId: string) => {
  const index = createTaskForm.value.selectedTests.findIndex(t => t.id === testId)
  if (index > -1) {
    createTaskForm.value.selectedTests.splice(index, 1)
  }
}

// 编辑任务相关方法
const isEditTestSelected = (testId: string) => {
  return editTaskForm.value.selectedTests.some(test => test.id === testId)
}

const toggleEditTest = (test: TestItem) => {
  const index = editTaskForm.value.selectedTests.findIndex(t => t.id === test.id)
  if (index > -1) {
    editTaskForm.value.selectedTests.splice(index, 1)
  } else {
    editTaskForm.value.selectedTests.push(test)
  }
}

const removeEditTest = (testId: string) => {
  const index = editTaskForm.value.selectedTests.findIndex(t => t.id === testId)
  if (index > -1) {
    editTaskForm.value.selectedTests.splice(index, 1)
  }
}

// 清除编辑任务中的所有已选测试
const clearAllEditTests = async () => {
  try {
    await ElMessageBox.confirm('确定要清除所有已选测试项目吗？', '确认清除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    editTaskForm.value.selectedTests = []
    ElMessage.success('已清除所有测试项目')
  } catch (error) {
    // 用户取消操作
  }
}

// 清除所有已选测试
const clearAllTests = async () => {
  try {
    await ElMessageBox.confirm('确定要清除所有已选测试项目吗？', '确认清除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    createTaskForm.value.selectedTests = []
    ElMessage.success('已清除所有测试项目')
  } catch (error) {
    // 用户取消操作
  }
}

// 测试任务说明历史记录
interface DescriptionHistory {
  id: string
  content: string
  createTime: Date
  usageCount: number
}

const descriptionHistory = ref<DescriptionHistory[]>([])
const showDescriptionHistory = ref(false)
const showEditDescription = ref(false)
const editingDescription = ref('')
const editingIndex = ref(-1)

// 报告生成相关
const showReportDialog = ref(false)
const reportData = ref<any>(null)
const diagnosisConclusion = ref('')
const reportTemplates = ref([
  { id: '1', name: '标准认知测试报告模板', content: '基于认知功能评估的标准报告模板' },
  { id: '2', name: '老年科专用报告模板', content: '针对老年患者认知功能的专业评估报告' },
  { id: '3', name: '神经内科报告模板', content: '神经内科认知功能评估专用报告模板' }
])
const selectedReportTemplate = ref('')

// 诊断结论模板相关
const showDiagnosisTemplateDialog = ref(false)
const selectedDiagnosisTemplateId = ref('')
const diagnosisTemplates = ref([
  {
    id: 'normal',
    name: '正常',
    level: '正常',
    type: 'success',
    content: '根据本次认知功能测试结果，患者各项认知指标均在正常范围内，认知功能良好。建议定期复查，保持健康的生活方式。'
  },
  {
    id: 'mild',
    name: '轻度异常',
    level: '轻度',
    type: 'warning',
    content: '根据本次认知功能测试结果，患者存在轻度认知功能异常，部分指标略低于正常范围。建议加强认知训练，定期复查，必要时进行进一步检查。'
  },
  {
    id: 'moderate',
    name: '中度异常',
    level: '中度',
    type: 'danger',
    content: '根据本次认知功能测试结果，患者存在中度认知功能异常，多项指标明显低于正常范围。建议及时就医，进行详细的神经心理学评估和相关检查。'
  },
  {
    id: 'severe',
    name: '重度异常',
    level: '重度',
    type: 'danger',
    content: '根据本次认知功能测试结果，患者存在重度认知功能异常，认知能力严重受损。强烈建议立即就医，进行全面的神经系统检查和治疗。'
  }
])



// 保存测试任务说明到历史记录
const saveDescription = () => {
  const content = createTaskForm.value.taskDescription.trim()
  if (!content) return

  // 检查是否已存在相同内容
  const existingIndex = descriptionHistory.value.findIndex(item => item.content === content)
  if (existingIndex > -1) {
    // 如果已存在，增加使用次数并移到最前面
    const existing = descriptionHistory.value[existingIndex]
    existing.usageCount++
    existing.createTime = new Date()
    descriptionHistory.value.splice(existingIndex, 1)
    descriptionHistory.value.unshift(existing)
  } else {
    // 新增记录
    const newRecord: DescriptionHistory = {
      id: Date.now().toString(),
      content,
      createTime: new Date(),
      usageCount: 1
    }
    descriptionHistory.value.unshift(newRecord)
  }

  // 限制历史记录数量为20条
  if (descriptionHistory.value.length > 20) {
    descriptionHistory.value = descriptionHistory.value.slice(0, 20)
  }

  ElMessage.success('已保存到历史记录')
}

// 引用历史记录到输入框
const useDescription = (item: DescriptionHistory) => {
  createTaskForm.value.taskDescription = item.content
  item.usageCount++
  showDescriptionHistory.value = false
  ElMessage.success('已引用到输入框')
}

// 编辑历史记录
const editDescription = (item: DescriptionHistory, index: number) => {
  editingDescription.value = item.content
  editingIndex.value = index
  showEditDescription.value = true
}

// 保存编辑的历史记录
const saveEditDescription = () => {
  const content = editingDescription.value.trim()
  if (!content) {
    ElMessage.warning('内容不能为空')
    return
  }

  if (editingIndex.value >= 0) {
    descriptionHistory.value[editingIndex.value].content = content
    ElMessage.success('修改成功')
    showEditDescription.value = false
    resetEditDescription()
  }
}

// 重置编辑状态
const resetEditDescription = () => {
  editingDescription.value = ''
  editingIndex.value = -1
}

// 删除历史记录
const deleteDescription = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条历史记录吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    descriptionHistory.value.splice(index, 1)
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消操作
  }
}

// 格式化日期时间
const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

// 获取简单网格项目样式
const getSnakeGridStyle = (index: number) => {
  const row = Math.floor(index / 3)
  const col = index % 3

  return {
    gridColumn: col + 1,
    gridRow: row + 1
  }
}



const createTask = async () => {
  if (!createTaskFormRef.value || !selectedPatient.value) return

  try {
    await createTaskFormRef.value.validate()
    creatingTask.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const newTask: TestTask = {
      id: Date.now().toString(),
      title: createTaskForm.value.title,
      taskNumber: createTaskForm.value.taskNumber,
      department: createTaskForm.value.department,
      patientId: selectedPatient.value.id,
      status: '未开始',
      createDate: new Date().toISOString().split('T')[0],
      tests: [...createTaskForm.value.selectedTests],
      completedCount: 0,
      totalCount: createTaskForm.value.selectedTests.length
    }

    currentTasks.value.unshift(newTask)
    showCreateTaskDialog.value = false

    ElMessage.success('测试任务创建成功')
  } catch (error) {
    console.error('创建任务失败:', error)
  } finally {
    creatingTask.value = false
  }
}

const resetCreateTaskForm = () => {
  createTaskForm.value = {
    title: '',
    taskNumber: '',
    department: '',
    selectedTests: [],
    taskDescription: ''
  }
  createTaskFormRef.value?.resetFields()
}

const resetEditTaskForm = () => {
  editTaskForm.value = {
    title: '',
    taskNumber: '',
    department: '',
    selectedTests: [],
    taskDescription: ''
  }
  editingTaskData.value = null
  editTaskFormRef.value?.resetFields()
}

const updateTask = async () => {
  if (!editTaskFormRef.value || !editingTaskData.value) return

  try {
    await editTaskFormRef.value.validate()
    editingTask.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新任务数据
    const taskIndex = currentTasks.value.findIndex(t => t.id === editingTaskData.value!.id)
    if (taskIndex > -1) {
      currentTasks.value[taskIndex] = {
        ...currentTasks.value[taskIndex],
        title: editTaskForm.value.title,
        department: editTaskForm.value.department,
        tests: [...editTaskForm.value.selectedTests],
        totalCount: editTaskForm.value.selectedTests.length
      }
    }

    showEditTaskDialog.value = false
    ElMessage.success('任务更新成功')
  } catch (error) {
    console.error('更新任务失败:', error)
  } finally {
    editingTask.value = false
  }
}

const handleTaskAction = ({ action, task }: { action: string, task: TestTask }) => {
  switch (action) {

    case 'edit':
      editTask(task)
      break
    case 'view':
      viewTaskDetail(task)
      break
    case 'delete':
      deleteTask(task)
      break
  }
}



const editTask = (task: TestTask) => {
  editingTaskData.value = task
  // 填充编辑表单
  editTaskForm.value = {
    title: task.title,
    taskNumber: task.taskNumber,
    department: task.department,
    selectedTests: [...task.tests],
    taskDescription: '' // 这里可以从任务中获取描述，如果有的话
  }
  showEditTaskDialog.value = true
}

const viewTaskDetail = (task: TestTask) => {
  viewingTaskData.value = task
  showTaskDetailDialog.value = true
}

const deleteTask = async (task: TestTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 ${task.taskNumber} 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = currentTasks.value.findIndex(t => t.id === task.id)
    if (index > -1) {
      currentTasks.value.splice(index, 1)
      ElMessage.success('任务删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

// 测试模板相关方法
const selectTemplate = (template: TestTemplate) => {
  selectedTemplate.value = template
}

const applyTemplate = () => {
  if (!selectedTemplate.value) return

  // 清空当前选择的测试
  createTaskForm.value.selectedTests = []

  // 应用模板中的测试项目
  createTaskForm.value.selectedTests = [...selectedTemplate.value.tests]

  // 关闭模板对话框
  showTemplateDialog.value = false

  ElMessage.success(`已应用模板：${selectedTemplate.value.name}`)
}

const resetTemplateSelection = () => {
  selectedTemplate.value = null
  templateSearchKeyword.value = ''
}

// 生命周期
onMounted(() => {
  // 默认选择第一个患者
  if (patients.value.length > 0) {
    selectedPatient.value = patients.value[0]
  }

  // 初始化测试任务数据
  currentTasks.value = [
    {
      id: '1',
      title: '20240105 神经内科 认知测试',
      taskNumber: 'SJ20240105001',
      department: '神经内科',
      patientId: '1',
      status: '未开始',
      createDate: '2024-01-05',
      tests: [
        { ...availableTests.value.find(t => t.id === '6')!, status: '待开始' }, // MMSE量表
        { ...availableTests.value.find(t => t.id === '5')!, status: '待开始' }, // 数字广度测试
        { ...availableTests.value.find(t => t.id === '8')!, status: '待开始' }, // 时钟绘制测试
      ],
      completedCount: 0,
      totalCount: 3
    },
    {
      id: '2',
      title: '20240104 精神科 认知测试',
      taskNumber: 'JS20240104002',
      department: '精神科',
      patientId: '2',
      status: '进行中',
      createDate: '2024-01-04',
      tests: [
        { ...availableTests.value.find(t => t.id === '7')!, status: '已完成' }, // MoCA量表
        { ...availableTests.value.find(t => t.id === '4')!, status: '待开始' }, // 词语流畅性测试
        { ...availableTests.value.find(t => t.id === '2')!, status: '待开始' }, // 连线测试A
      ],
      completedCount: 1,
      totalCount: 3
    },
    {
      id: '3',
      title: '20240103 老年科 认知测试',
      taskNumber: 'LN20240103003',
      department: '老年科',
      patientId: '3',
      status: '进行中',
      createDate: '2024-01-03',
      tests: [
        { ...availableTests.value.find(t => t.id === '1')!, status: '已完成' }, // PDQ5量表
        { ...availableTests.value.find(t => t.id === '6')!, status: '已完成' }, // MMSE量表
        { ...availableTests.value.find(t => t.id === '8')!, status: '已完成' }, // 时钟绘制测试
      ],
      completedCount: 3,
      totalCount: 3
    }
  ]

  // 自动计算所有任务的状态
  currentTasks.value.forEach(task => {
    updateTaskStatus(task)
  })
})
</script>

<style scoped>
.patient-management {
  height: 100%;
  width: 100%;
}

.patient-layout {
  display: flex;
  height: 100%;
  gap: 24px;
}

/* 左侧患者列表 */
.patient-list-section {
  flex: 2;
  background: white;
  border-radius: 8px;
  padding: 24px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.patient-search {
  margin-bottom: 16px;
}

.patient-list {
  flex: 1;
  overflow-y: auto;
}

.patient-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.patient-item:hover {
  background-color: #f9fafb;
  border-color: #e5e7eb;
}

.patient-item.active {
  background-color: #eff6ff;
  border-color: #3b82f6;
}

.patient-avatar {
  margin-right: 12px;
}

.patient-info {
  flex: 1;
  min-width: 0;
}

.patient-name {
  font-weight: 600;
  font-size: 16px;
  color: #1f2937;
  margin-bottom: 4px;
}

.patient-details {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
}

.patient-id,
.patient-age,
.patient-gender {
  font-size: 12px;
  color: #6b7280;
}



.patient-actions {
  margin-left: 8px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* 右侧患者详情 */
.patient-detail-section {
  flex: 5;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.patient-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 上部分：患者信息卡片 - 高度比例 2/5 */
.patient-info-card {
  flex: 2;
  background: white;
  border-radius: 8px;
  padding: 14px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.patient-basic-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.basic-info-text h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.info-tags {
  display: flex;
  gap: 8px;
}

.detail-actions {
  display: flex;
  gap: 12px;
}

.patient-tabs {
  flex: 1;
  overflow: hidden;
}

.patient-tabs :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.info-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 16px;
  padding: 8px 0;
  margin-bottom: 8px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.info-item span {
  font-size: 16px;
  color: #1f2937;
}

.test-history {
  padding: 20px 0;
}

.test-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

.summary-card {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.summary-number {
  font-size: 32px;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 4px;
}

.summary-label {
  font-size: 14px;
  color: #6b7280;
}

.test-table {
  margin-top: 16px;
}

/* 测试历史表格样式 */
.test-table :deep(.el-table__header-wrapper .el-table__cell) {
  text-align: center;
  background-color: #f8fafc;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
}

.test-table :deep(.el-table__body-wrapper .el-table__cell) {
  text-align: center;
}

.reports-section {
  padding: 20px 0;
}

.reports-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.reports-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.reports-table {
  margin-top: 16px;
}

/* 报告中心表格样式 */
.reports-table :deep(.el-table__header-wrapper .el-table__cell) {
  text-align: center;
  background-color: #f8fafc;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
}

.reports-table :deep(.el-table__body-wrapper .el-table__cell) {
  text-align: center;
}

.no-patient-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 下部分：测试任务分配 - 高度比例 3/5 */
.test-assignment-section {
  flex: 3;
  background: white;
  border-radius: 8px;
  padding: 24px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.assignment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.assignment-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.current-tasks {
  flex: 1;
  overflow-y: auto;
}

.no-tasks {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s;
}

.task-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.task-item.task-running {
  border-color: #f59e0b;
  background-color: #fffbeb;
}

.task-info {
  flex: 1;
  min-width: 0;
}

.task-title {
  font-weight: 600;
  font-size: 16px;
  color: #1f2937;
  margin-bottom: 4px;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.task-number {
  font-size: 12px;
  color: #6b7280;
  font-family: monospace;
}

.task-date {
  font-size: 12px;
  color: #9ca3af;
}

.task-tests {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.test-tag {
  font-size: 11px;
}

.task-progress {
  margin: 0 16px;
  min-width: 120px;
}

.progress-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.progress-info span {
  font-size: 12px;
  color: #6b7280;
}

.task-actions {
  margin-left: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
  height: auto;
  min-height: 28px;
}

/* 任务详情对话框样式 */
.task-detail-content {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

/* 重复的3列info-grid样式已删除 - 保持4列布局 */

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.info-item span {
  font-size: 14px;
  color: #1f2937;
}

.test-items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.test-item-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s;
  min-height: 60px;
}

.test-item-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.test-order {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.test-content {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}

.test-name {
  font-weight: 600;
  font-size: 15px;
  color: #1f2937;
}

.test-status {
  flex-shrink: 0;
}



.progress-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.progress-bar {
  margin-top: 16px;
}

/* 创建任务对话框样式 */
.test-selection {
  display: flex;
  gap: 32px;
  height: 600px;
  width: 100%;
  min-width: 1200px;
}

.available-tests {
  flex: 2;
  display: flex;
  flex-direction: column;
  min-width: 300px;
}

/* 右侧面板整体布局 */
.right-panel {
  flex: 4;
  display: flex;
  flex-direction: column;
  min-width: 500px;
  height: 600px; /* 设置固定高度 */
}

/* 已选测试项目区域 (占4/6) */
.selected-tests-section {
  flex: 4;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex子项收缩 */
}

.selected-tests {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 测试任务说明区域 (占2/6) */
.description-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  border-top: 1px solid #e5e7eb;
  min-height: 0; /* 允许flex子项收缩 */
}

.tests-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tests-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.selected-tests-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.selected-tests-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.clear-all-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  padding: 4px 8px;
  height: 28px;
}

/* 测试任务说明区域样式 */
.task-description-section {
  width: 100%;
  height: 100%;
  padding: 16px;
  background: #f8fafc;
  border-radius: 0 0 8px 8px;
  display: flex;
  flex-direction: column;
}

.description-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  flex-shrink: 0;
}

.task-description-section h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-description-section h4::before {
  content: '📝';
  font-size: 14px;
}

.description-input {
  flex: 1;
  margin-bottom: 12px;
}

.description-input .el-textarea__inner {
  resize: none;
}

.description-actions {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

/* 历史记录对话框样式 */
.description-history {
  max-height: 500px;
  overflow-y: auto;
}

.no-history {
  text-align: center;
  color: #9ca3af;
  padding: 40px 0;
  font-size: 14px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s;
}

.history-item:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.history-content {
  flex: 1;
  margin-right: 16px;
}

.history-text {
  font-size: 14px;
  color: #1f2937;
  line-height: 1.5;
  margin-bottom: 8px;
  word-break: break-word;
}

.history-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #6b7280;
}

.history-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

/* 对话框底部样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

.test-search {
  margin-bottom: 16px;
}

.test-list {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.test-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: all 0.2s;
}

.test-item:last-child {
  border-bottom: none;
}

.test-item:hover {
  background-color: #f9fafb;
}

.test-item.selected {
  background-color: #eff6ff;
  border-color: #3b82f6;
}

.test-checkbox {
  margin-right: 12px;
}

.test-info {
  flex: 1;
  min-width: 0;
}

.test-name {
  font-weight: 500;
  font-size: 14px;
  color: #1f2937;
}

.selected-tests {
  border-left: 1px solid #e5e7eb;
  padding-left: 32px;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #9ca3af;
  font-size: 14px;
}

.selected-grid-flow {
  display: grid;
  grid-template-columns: repeat(3, 240px);
  grid-gap: 5px 5px;
  padding: 10px 5px;
  justify-content: space-evenly;
  flex: 1;
  overflow-y: auto;
  width: 100%;
  min-height: 0; /* 允许flex子项收缩 */
}

.grid-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}



.selected-card {
  display: flex;
  flex-direction: column;
  padding: 12px 16px;
  background: #e0e7ff;
  border-radius: 8px;
  cursor: move;
  border: 2px solid #3b82f6;
  transition: all 0.2s;
  position: relative;
  width: 240px;
  height: 80px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.selected-card:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-order {
  position: absolute;
  top: -10px;
  left: -10px;
  width: 26px;
  height: 26px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  border: 3px solid white;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  z-index: 20;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 4px;
  margin-top: 10px;
  margin-left: 10px;
}

.card-name {
  font-weight: 600;
  font-size: 13px;
  color: #1f2937;
  line-height: 1.2;
  text-align: center;
}

.selected-card .el-button {
  position: absolute;
  top: 4px;
  right: 4px;
  padding: 4px;
}

.estimated-time {
  font-size: 16px;
  font-weight: 600;
  color: #3b82f6;
}

/* 测试模板对话框样式 - 完全重构 */
.template-selection-container {
  display: flex !important;
  flex-direction: column !important;
  height: 500px;
  width: 100% !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
}

/* 强制重置所有文本方向 - 使用新的类名 */
.template-selection-container *,
.template-list-container *,
.template-item-container *,
.template-content-section * {
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  text-align: left !important;
}

.template-search-section {
  margin-bottom: 16px;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
}

.template-list-container {
  flex: 1 !important;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  width: 100% !important;
  max-width: 100% !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
}

.template-item-container {
  display: flex !important;
  flex-direction: row !important;
  align-items: flex-start !important;
  padding: 16px !important;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: all 0.2s;
  width: 100% !important;
  box-sizing: border-box !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
}

.template-item-container:last-child {
  border-bottom: none;
}

.template-item-container:hover {
  background-color: #f9fafb;
}

.template-item-container.template-selected {
  background-color: #eff6ff;
  border-color: #3b82f6;
}

.template-radio-section {
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0 !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
}

.template-content-section {
  flex: 1 !important;
  min-width: 0 !important;
  width: 100% !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
}

.template-title {
  font-weight: 600 !important;
  font-size: 16px !important;
  color: #1f2937 !important;
  margin-bottom: 8px !important;
  white-space: normal !important;
  word-wrap: break-word !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
  text-align: left !important;
}

.template-desc {
  font-size: 14px !important;
  color: #6b7280 !important;
  margin-bottom: 8px !important;
  line-height: 1.4 !important;
  white-space: normal !important;
  word-wrap: break-word !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
  text-align: left !important;
}

.template-test-list {
  font-size: 13px !important;
  color: #6b7280 !important;
  line-height: 1.5 !important;
  white-space: normal !important;
  word-wrap: break-word !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
  text-align: left !important;
}

.test-item-name {
  color: #3b82f6 !important;
  display: inline !important;
  white-space: normal !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
  text-align: left !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .patient-layout {
    flex-direction: column;
    height: auto;
  }

  .patient-list-section {
    flex: none;
    height: 400px;
  }

  .patient-detail-section {
    flex: none;
    min-height: 600px;
  }
}

@media (max-width: 768px) {
  .patient-layout {
    gap: 16px;
  }

  .patient-list-section,
  .patient-detail-section {
    padding: 16px;
  }

  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  /* 移动端info-grid样式已禁用 - 仅支持PC端4列布局 */

  .test-summary {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .selected-grid-flow {
    grid-template-columns: 1fr;
    grid-gap: 20px;
    justify-content: center;
  }

  .right-panel {
    height: auto;
    min-width: 300px;
  }

  .selected-tests-section {
    flex: none;
    min-height: 300px;
  }

  .description-section {
    flex: none;
    min-height: 200px;
  }

  .selected-card {
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
  }

  .task-description-section {
    padding: 12px;
  }

  .description-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .description-actions {
    align-self: stretch;
  }

  .description-actions .el-button {
    flex: 1;
    font-size: 12px;
  }

  .dialog-footer {
    justify-content: center;
  }

  .footer-actions {
    justify-content: center;
  }
}

/* 平板设备媒体查询已禁用 - 仅支持PC端4列布局 */

/* 报告生成相关样式 */
.report-dialog {
  margin: 0 !important;
}

.report-dialog .el-dialog {
  margin: 0 auto !important;
  width: 90% !important;
  max-width: 90% !important;
  height: 80vh !important;
  max-height: 80vh !important;
  border-radius: 8px !important;
}



.report-dialog .el-dialog__body {
  padding: 0 !important;
  height: 80vh !important;
  overflow: hidden !important;
  margin: 0 !important;
}

.report-dialog .el-dialog__footer {
  display: none !important;
}



.report-generator {
  display: flex;
  height: 80vh;
  background: #f5f5f5;
}

/* 右侧控制面板 */
.report-controls {
  width: 350px;
  background: white;
  border-left: 1px solid #e5e7eb;
  padding: 0;
  overflow-y: auto;
  flex-shrink: 0;
  order: 2;
}

.control-section {
  margin-bottom: 24px;
  padding: 20px 20px 20px 20px;
  border-bottom: 1px solid #eee;
}

.control-section:last-child {
  border-bottom: none;
}

.control-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.template-radio {
  display: block;
  margin-bottom: 8px;
  width: 100%;
}

.diagnosis-input {
  margin-bottom: 12px;
}

.template-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.template-buttons .el-button {
  font-size: 12px;
  padding: 4px 8px;
}

.report-controls .action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.report-controls .action-buttons .el-button {
  width: 100%;
}

.save-section {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #eee;
}

.save-btn {
  width: 100%;
}

/* 诊断结论头部样式 */
.diagnosis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.diagnosis-header h4 {
  margin: 0;
}

.template-trigger-btn {
  flex-shrink: 0;
}

/* 诊断模板对话框样式 */
.diagnosis-template-selection {
  max-height: 400px;
  overflow-y: auto;
}

.diagnosis-template-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.diagnosis-template-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.diagnosis-template-item.active {
  border-color: #409eff;
  background-color: #e6f7ff;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-preview {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-left: 24px;
}

/* 左侧A4预览区域 */
.report-preview-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #f0f0f0;
  display: flex;
  justify-content: center;
  order: 1;
  align-items: flex-start;
}

.a4-pages {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  width: 100%;
}

/* A4页面样式 */
.a4-page {
  width: 210mm;
  min-height: 297mm;
  background: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 20mm;
  box-sizing: border-box;
  font-family: 'Microsoft YaHei', sans-serif;
  font-size: 12px;
  line-height: 1.6;
  position: relative;
  page-break-after: always;
  margin-bottom: 20px;
  flex-shrink: 0;
}

/* 报告头部 */
.report-header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
}

.report-header h1 {
  margin: 0 0 20px 0;
  font-size: 24px;
  color: #1f2937;
  font-weight: bold;
}

.report-header-simple {
  text-align: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
}

.report-header-simple h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #1f2937;
}

.patient-info-simple {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.report-info {
  text-align: left;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-row span {
  flex: 1;
}

/* 报告内容 */
.report-content h2 {
  margin: 20px 0 15px 0;
  font-size: 16px;
  color: #1f2937;
  border-bottom: 1px solid #ddd;
  padding-bottom: 5px;
  font-weight: bold;
}

.test-results-table {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
  font-size: 12px;
}

.test-results-table th,
.test-results-table td {
  border: 1px solid #333;
  padding: 8px;
  text-align: center;
}

.test-results-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.analysis-content {
  margin: 15px 0;
  text-align: justify;
}

.analysis-content p {
  margin: 10px 0;
}

.analysis-content ul {
  margin: 10px 0;
  padding-left: 20px;
}

.analysis-content li {
  margin: 5px 0;
}

.diagnosis-content {
  margin: 15px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fafafa;
  min-height: 80px;
  line-height: 1.8;
  text-align: justify;
}

.diagnosis-content .placeholder {
  color: #999;
  font-style: italic;
}

.recommendations {
  margin: 15px 0;
}

.recommendations ol {
  padding-left: 20px;
}

.recommendations li {
  margin: 8px 0;
}

.report-signature {
  margin-top: 40px;
}

.signature-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.signature-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.signature-line {
  border-bottom: 1px solid #333;
  width: 150px;
  height: 20px;
}

/* 页脚 */
.page-footer {
  position: absolute;
  bottom: 15mm;
  right: 20mm;
  font-size: 10px;
  color: #666;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header .section-title {
  margin: 0;
}

/* 打印样式 */
@media print {
  .report-controls {
    display: none !important;
  }

  .report-preview-container {
    padding: 0 !important;
    background: white !important;
  }

  .a4-page {
    box-shadow: none !important;
    margin: 0 !important;
    page-break-after: always;
  }

  .page-footer {
    position: fixed;
    bottom: 15mm;
  }
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .report-controls {
    width: 300px;
  }

  .a4-page {
    transform: scale(0.8);
    transform-origin: top center;
  }
}

@media (max-width: 1200px) {
  .report-controls {
    width: 280px;
  }

  .a4-page {
    transform: scale(0.7);
  }
}

/* 滚动条样式 */
.report-controls::-webkit-scrollbar,
.report-preview-container::-webkit-scrollbar {
  width: 6px;
}

.report-controls::-webkit-scrollbar-track,
.report-preview-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.report-controls::-webkit-scrollbar-thumb,
.report-preview-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.report-controls::-webkit-scrollbar-thumb:hover,
.report-preview-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
