<template>
  <div class="test-management-container">
    <div class="page-header">
      <h1 class="page-title">PDQ-5测试管理</h1>
      <p class="page-description">管理PDQ-5量表测试的配置、数据和结果分析</p>
    </div>

    <div class="content-wrapper">
      <!-- 统计卡片 -->
      <div class="stats-grid">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.totalTests }}</div>
            <div class="stat-label">总测试数</div>
          </div>
          <el-icon class="stat-icon"><Document /></el-icon>
        </el-card>
        
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.completedTests }}</div>
            <div class="stat-label">已完成</div>
          </div>
          <el-icon class="stat-icon"><Check /></el-icon>
        </el-card>
        
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.avgScore }}</div>
            <div class="stat-label">平均分</div>
          </div>
          <el-icon class="stat-icon"><TrendCharts /></el-icon>
        </el-card>
        
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.activePatients }}</div>
            <div class="stat-label">活跃患者</div>
          </div>
          <el-icon class="stat-icon"><User /></el-icon>
        </el-card>
      </div>

      <!-- 操作工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleCreateTest">
            <el-icon><Plus /></el-icon>
            创建新测试
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchQuery"
            placeholder="搜索患者或测试..."
            style="width: 300px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 测试列表 -->
      <el-card class="table-card">
        <el-table :data="filteredTests" style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="测试ID" width="120" />
          <el-table-column prop="patientName" label="患者姓名" width="120" />
          <el-table-column prop="patientId" label="患者ID" width="120" />
          <el-table-column prop="createTime" label="创建时间" width="180" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="score" label="得分" width="80" />
          <el-table-column prop="duration" label="用时(分钟)" width="100" />
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button size="small" @click="handleView(row)">查看</el-button>
              <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
              <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalTests"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  Check,
  TrendCharts,
  User,
  Plus,
  Download,
  Search
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalTests = ref(0)

// 统计数据
const stats = ref({
  totalTests: 156,
  completedTests: 142,
  avgScore: 78.5,
  activePatients: 89
})

// 测试数据
const tests = ref([
  {
    id: 'PDQ001',
    patientName: '张三',
    patientId: 'P001',
    createTime: '2024-01-15 10:30:00',
    status: 'completed',
    score: 85,
    duration: 12
  },
  {
    id: 'PDQ002',
    patientName: '李四',
    patientId: 'P002',
    createTime: '2024-01-15 14:20:00',
    status: 'in_progress',
    score: null,
    duration: null
  },
  // 更多测试数据...
])

// 计算属性
const filteredTests = computed(() => {
  if (!searchQuery.value) return tests.value
  
  return tests.value.filter(test => 
    test.patientName.includes(searchQuery.value) ||
    test.patientId.includes(searchQuery.value) ||
    test.id.includes(searchQuery.value)
  )
})

// 方法
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'completed': 'success',
    'in_progress': 'warning',
    'pending': 'info',
    'failed': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'completed': '已完成',
    'in_progress': '进行中',
    'pending': '待开始',
    'failed': '失败'
  }
  return textMap[status] || '未知'
}

const handleCreateTest = () => {
  ElMessage.info('创建新测试功能开发中...')
}

const handleExport = () => {
  ElMessage.info('导出数据功能开发中...')
}

const handleView = (row: any) => {
  ElMessage.info(`查看测试 ${row.id}`)
}

const handleEdit = (row: any) => {
  ElMessage.info(`编辑测试 ${row.id}`)
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除测试 ${row.id} 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    ElMessage.success('删除成功')
    // 这里应该调用删除API
  } catch (error) {
    // 用户取消操作
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  // 重新加载数据
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  // 重新加载数据
}

// 生命周期
onMounted(() => {
  // 初始化数据加载
  totalTests.value = tests.value.length
})
</script>

<style scoped>
.test-management-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.content-wrapper {
  max-width: 1200px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.stat-icon {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 24px;
  color: #3b82f6;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.table-card {
  border-radius: 8px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}
</style>
